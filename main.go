package main

import (
	"context"
	"goHomework/config"
	"goHomework/handler"
	"goHomework/middleware"
	"goHomework/service"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建服务实例
	customerService := service.NewCustomerService()
	customerHandler := handler.NewCustomerHandler(customerService)

	// 创建Gin路由器
	r := setupRouter(cfg, customerHandler)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         cfg.GetAddress(),
		Handler:      r,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// 启动服务器
	go func() {
		log.Printf("服务器启动在 %s (模式: %s)", cfg.GetAddress(), cfg.Server.Mode)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 优雅关闭
	gracefulShutdown(server)
}

// setupRouter 设置路由
func setupRouter(cfg *config.Config, customerHandler *handler.CustomerHandler) *gin.Engine {
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Recovery())
	r.Use(middleware.Logger(middleware.LoggerConfig{
		SkipPaths: cfg.Logger.SkipPaths,
	}))

	// 速率限制
	if cfg.RateLimit.Enabled {
		rateLimiter := middleware.NewRateLimiter(cfg.RateLimit.Rate, cfg.RateLimit.Window)
		r.Use(rateLimiter.RateLimit())
	}

	// 配置CORS
	corsConfig := cors.Config{
		AllowOrigins:     cfg.CORS.AllowOrigins,
		AllowMethods:     cfg.CORS.AllowMethods,
		AllowHeaders:     cfg.CORS.AllowHeaders,
		ExposeHeaders:    cfg.CORS.ExposeHeaders,
		AllowCredentials: cfg.CORS.AllowCredentials,
		MaxAge:           time.Duration(cfg.CORS.MaxAge) * time.Second,
	}
	r.Use(cors.New(corsConfig))

	// 健康检查端点
	r.GET("/health", healthCheck)
	r.GET("/metrics", metricsHandler)

	// API路由组
	setupAPIRoutes(r, customerHandler)

	return r
}

// setupAPIRoutes 设置API路由
func setupAPIRoutes(r *gin.Engine, customerHandler *handler.CustomerHandler) {
	// API v1 路由组
	v1 := r.Group("/api/v1")
	{
		// 客户相关路由
		customers := v1.Group("/customers")
		{
			customers.GET("", customerHandler.GetCustomerList)                   // 获取客户列表（支持分页和搜索）
			customers.GET("/all", customerHandler.GetAllCustomers)               // 获取所有客户（兼容旧版本）
			customers.GET("/search", customerHandler.SearchCustomers)            // 搜索客户
			customers.GET("/stats", customerHandler.GetCustomerStats)            // 获取统计信息
			customers.GET("/:id", customerHandler.GetCustomerByID)               // 获取单个客户
			customers.POST("", customerHandler.CreateCustomer)                   // 创建客户
			customers.PUT("/:id", customerHandler.UpdateCustomer)                // 更新客户
			customers.PATCH("/:id/status", customerHandler.UpdateCustomerStatus) // 更新客户状态
			customers.DELETE("/:id", customerHandler.DeleteCustomer)             // 删除客户
			customers.DELETE("/batch", customerHandler.BatchDeleteCustomers)     // 批量删除客户
		}
	}
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"message":   "服务器运行正常",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		"version":   "1.0.0",
	})
}

// metricsHandler 指标处理器
func metricsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"uptime":    time.Since(startTime).String(),
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		"version":   "1.0.0",
	})
}

// gracefulShutdown 优雅关闭
func gracefulShutdown(server *http.Server) {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("服务器强制关闭: %v", err)
	}

	log.Println("服务器已关闭")
}

var startTime = time.Now()
