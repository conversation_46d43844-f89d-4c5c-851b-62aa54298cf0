package handler

import (
	"goHomework/model"
	"goHomework/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CustomerHandler struct {
	customerService *service.CustomerService
}

func NewCustomerHandler(customerService *service.CustomerService) *CustomerHandler {
	return &CustomerHandler{
		customerService: customerService,
	}
}

// GetAllCustomers 获取所有客户
func (h *CustomerHandler) GetAllCustomers(c *gin.Context) {
	customers := h.customerService.GetAllCustomers()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    customers,
		"message": "获取客户列表成功",
	})
}

// GetCustomerByID 根据ID获取客户
func (h *CustomerHandler) GetCustomerByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的客户ID",
		})
		return
	}

	customer, err := h.customerService.GetCustomerByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "客户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    customer,
		"message": "获取客户信息成功",
	})
}

// CreateCustomer 创建新客户
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	var customer model.Customer
	if err := c.ShouldBindJSON(&customer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求数据格式错误",
		})
		return
	}

	// 简单验证
	if customer.Name == "" || customer.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "姓名和电话不能为空",
		})
		return
	}

	createdCustomer := h.customerService.CreateCustomer(customer)
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    createdCustomer,
		"message": "创建客户成功",
	})
}

// UpdateCustomer 更新客户信息
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的客户ID",
		})
		return
	}

	var customer model.Customer
	if err := c.ShouldBindJSON(&customer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求数据格式错误",
		})
		return
	}

	// 简单验证
	if customer.Name == "" || customer.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "姓名和电话不能为空",
		})
		return
	}

	updatedCustomer, err := h.customerService.UpdateCustomer(id, customer)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "客户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updatedCustomer,
		"message": "更新客户信息成功",
	})
}

// DeleteCustomer 删除客户
func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的客户ID",
		})
		return
	}

	err = h.customerService.DeleteCustomer(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "客户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除客户成功",
	})
}
