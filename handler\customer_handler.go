package handler

import (
	"errors"
	"goHomework/model"
	"goHomework/service"
	"regexp"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type CustomerHandler struct {
	customerService *service.CustomerService
}

func NewCustomerHandler(customerService *service.CustomerService) *CustomerHandler {
	return &CustomerHandler{
		customerService: customerService,
	}
}

// GetAllCustomers 获取所有客户（已废弃，请使用 GetCustomerList）
func (h *CustomerHandler) GetAllCustomers(c *gin.Context) {
	customers := h.customerService.GetAllCustomers()
	resp := model.NewResponseBuilder(c)
	resp.Success(customers, "获取客户列表成功")
}

// GetCustomerList 获取客户列表（支持分页和搜索）
func (h *CustomerHandler) GetCustomerList(c *gin.Context) {
	var req model.CustomerListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		resp := model.NewResponseBuilder(c)
		resp.ValidationError(h.formatValidationErrors(err))
		return
	}

	result, err := h.customerService.GetCustomerList(req)
	if err != nil {
		resp := model.NewResponseBuilder(c)
		resp.InternalError("获取客户列表失败")
		return
	}

	resp := model.NewResponseBuilder(c)
	pagination := &model.PaginationInfo{
		Page:       result.Page,
		PageSize:   result.PageSize,
		Total:      result.Total,
		TotalPages: result.TotalPages,
	}
	resp.List(result.Data, pagination, "获取客户列表成功")
}

// GetCustomerByID 根据ID获取客户
func (h *CustomerHandler) GetCustomerByID(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		resp.BadRequest("无效的客户ID", nil)
		return
	}

	customer, err := h.customerService.GetCustomerByID(id)
	if err != nil {
		resp.NotFound("客户不存在")
		return
	}

	resp.Success(customer, "获取客户信息成功")
}

// CreateCustomer 创建新客户
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	var req model.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ValidationError(h.formatValidationErrors(err))
		return
	}

	// 额外验证
	if err := h.validateCreateRequest(req); err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	createdCustomer, err := h.customerService.CreateCustomer(req)
	if err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	resp.Created(createdCustomer, "创建客户成功")
}

// UpdateCustomer 更新客户信息
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		resp.BadRequest("无效的客户ID", nil)
		return
	}

	var req model.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ValidationError(h.formatValidationErrors(err))
		return
	}

	// 额外验证
	if err := h.validateUpdateRequest(req); err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	updatedCustomer, err := h.customerService.UpdateCustomer(id, req)
	if err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	resp.Success(updatedCustomer, "更新客户信息成功")
}

// DeleteCustomer 删除客户
func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		resp.BadRequest("无效的客户ID", nil)
		return
	}

	err = h.customerService.DeleteCustomer(id)
	if err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	resp.Success(nil, "删除客户成功")
}

// UpdateCustomerStatus 更新客户状态
func (h *CustomerHandler) UpdateCustomerStatus(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		resp.BadRequest("无效的客户ID", nil)
		return
	}

	var req struct {
		Status model.UserStatus `json:"status" binding:"required,oneof=active inactive blocked deleted"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ValidationError(h.formatValidationErrors(err))
		return
	}

	err = h.customerService.UpdateCustomerStatus(id, req.Status)
	if err != nil {
		resp.BadRequest(err.Error(), nil)
		return
	}

	resp.Success(nil, "更新客户状态成功")
}

// BatchDeleteCustomers 批量删除客户
func (h *CustomerHandler) BatchDeleteCustomers(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	var req struct {
		IDs []int `json:"ids" binding:"required,min=1"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ValidationError(h.formatValidationErrors(err))
		return
	}

	err := h.customerService.BatchDeleteCustomers(req.IDs)
	if err != nil {
		resp.InternalError("批量删除失败")
		return
	}

	resp.Success(nil, "批量删除成功")
}

// GetCustomerStats 获取客户统计信息
func (h *CustomerHandler) GetCustomerStats(c *gin.Context) {
	resp := model.NewResponseBuilder(c)
	stats := h.customerService.GetCustomerStats()
	resp.Success(stats, "获取统计信息成功")
}

// SearchCustomers 搜索客户
func (h *CustomerHandler) SearchCustomers(c *gin.Context) {
	resp := model.NewResponseBuilder(c)

	keyword := c.Query("keyword")
	if keyword == "" {
		resp.BadRequest("搜索关键词不能为空", nil)
		return
	}

	customers := h.customerService.SearchCustomers(keyword)
	resp.Success(customers, "搜索完成")
}

// formatValidationErrors 格式化验证错误
func (h *CustomerHandler) formatValidationErrors(err error) interface{} {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		errors := make(map[string]string)
		for _, fieldError := range validationErrors {
			field := fieldError.Field()
			tag := fieldError.Tag()

			switch tag {
			case "required":
				errors[field] = field + "是必填字段"
			case "email":
				errors[field] = "邮箱格式不正确"
			case "min":
				errors[field] = field + "长度不能少于" + fieldError.Param() + "个字符"
			case "max":
				errors[field] = field + "长度不能超过" + fieldError.Param() + "个字符"
			case "len":
				errors[field] = field + "长度必须为" + fieldError.Param() + "个字符"
			case "oneof":
				errors[field] = field + "的值不在允许范围内"
			default:
				errors[field] = field + "验证失败"
			}
		}
		return errors
	}
	return err.Error()
}

// validateCreateRequest 验证创建请求
func (h *CustomerHandler) validateCreateRequest(req model.CreateCustomerRequest) error {
	// 验证邮箱格式
	if req.Email != "" {
		if err := h.validateEmail(req.Email); err != nil {
			return err
		}
	}

	// 验证手机号格式
	if req.Phone != "" {
		if err := h.validatePhone(req.Phone); err != nil {
			return err
		}
	}

	return nil
}

// validateUpdateRequest 验证更新请求
func (h *CustomerHandler) validateUpdateRequest(req model.UpdateCustomerRequest) error {
	// 验证邮箱格式
	if req.Email != nil && *req.Email != "" {
		if err := h.validateEmail(*req.Email); err != nil {
			return err
		}
	}

	// 验证手机号格式
	if req.Phone != nil && *req.Phone != "" {
		if err := h.validatePhone(*req.Phone); err != nil {
			return err
		}
	}

	return nil
}

// validateEmail 验证邮箱格式
func (h *CustomerHandler) validateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return errors.New("邮箱格式不正确")
	}
	return nil
}

// validatePhone 验证手机号格式
func (h *CustomerHandler) validatePhone(phone string) error {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		return errors.New("手机号格式不正确")
	}
	return nil
}
