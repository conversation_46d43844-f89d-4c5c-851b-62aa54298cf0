package model

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse 统一API响应结构
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     *ErrorInfo  `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// ErrorInfo 错误信息结构
type ErrorInfo struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// ListResponse 列表响应结构
type ListResponse struct {
	Data       interface{}     `json:"data"`
	Pagination *PaginationInfo `json:"pagination,omitempty"`
}

// 响应构建器
type ResponseBuilder struct {
	ctx *gin.Context
}

// NewResponseBuilder 创建响应构建器
func NewResponseBuilder(ctx *gin.Context) *ResponseBuilder {
	return &ResponseBuilder{ctx: ctx}
}

// Success 成功响应
func (rb *ResponseBuilder) Success(data interface{}, message string) {
	response := APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
		RequestID: rb.getRequestID(),
	}
	rb.ctx.JSON(http.StatusOK, response)
}

// Created 创建成功响应
func (rb *ResponseBuilder) Created(data interface{}, message string) {
	response := APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
		RequestID: rb.getRequestID(),
	}
	rb.ctx.JSON(http.StatusCreated, response)
}

// Error 错误响应
func (rb *ResponseBuilder) Error(statusCode int, code, message string, details interface{}) {
	response := APIResponse{
		Success: false,
		Message: message,
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
			Details: details,
		},
		Timestamp: time.Now(),
		RequestID: rb.getRequestID(),
	}
	rb.ctx.JSON(statusCode, response)
}

// BadRequest 400错误响应
func (rb *ResponseBuilder) BadRequest(message string, details interface{}) {
	rb.Error(http.StatusBadRequest, "BAD_REQUEST", message, details)
}

// NotFound 404错误响应
func (rb *ResponseBuilder) NotFound(message string) {
	rb.Error(http.StatusNotFound, "NOT_FOUND", message, nil)
}

// InternalError 500错误响应
func (rb *ResponseBuilder) InternalError(message string) {
	rb.Error(http.StatusInternalServerError, "INTERNAL_ERROR", message, nil)
}

// ValidationError 验证错误响应
func (rb *ResponseBuilder) ValidationError(details interface{}) {
	rb.Error(http.StatusBadRequest, "VALIDATION_ERROR", "请求数据验证失败", details)
}

// List 列表响应
func (rb *ResponseBuilder) List(data interface{}, pagination *PaginationInfo, message string) {
	listResponse := ListResponse{
		Data:       data,
		Pagination: pagination,
	}
	rb.Success(listResponse, message)
}

// getRequestID 获取请求ID
func (rb *ResponseBuilder) getRequestID() string {
	if requestID := rb.ctx.GetString("request_id"); requestID != "" {
		return requestID
	}
	return ""
}

// 常用错误代码常量
const (
	ErrCodeValidation    = "VALIDATION_ERROR"
	ErrCodeNotFound      = "NOT_FOUND"
	ErrCodeDuplicate     = "DUPLICATE_ERROR"
	ErrCodeUnauthorized  = "UNAUTHORIZED"
	ErrCodeForbidden     = "FORBIDDEN"
	ErrCodeInternalError = "INTERNAL_ERROR"
	ErrCodeBadRequest    = "BAD_REQUEST"
)
