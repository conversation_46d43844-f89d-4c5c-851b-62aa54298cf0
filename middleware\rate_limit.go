package middleware

import (
	"goHomework/model"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 速率限制器
type RateLimiter struct {
	visitors map[string]*Visitor
	mutex    sync.RWMutex
	rate     int           // 每分钟允许的请求数
	window   time.Duration // 时间窗口
}

// Visitor 访问者信息
type Visitor struct {
	requests []time.Time
	mutex    sync.Mutex
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(rate int, window time.Duration) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		window:   window,
	}
	
	// 启动清理协程
	go rl.cleanup()
	
	return rl
}

// RateLimit 速率限制中间件
func (rl *RateLimiter) RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		if !rl.allow(ip) {
			resp := model.NewResponseBuilder(c)
			resp.Error(http.StatusTooManyRequests, "RATE_LIMIT_EXCEEDED", "请求过于频繁，请稍后再试", nil)
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// allow 检查是否允许请求
func (rl *RateLimiter) allow(ip string) bool {
	rl.mutex.Lock()
	visitor, exists := rl.visitors[ip]
	if !exists {
		visitor = &Visitor{
			requests: make([]time.Time, 0),
		}
		rl.visitors[ip] = visitor
	}
	rl.mutex.Unlock()

	visitor.mutex.Lock()
	defer visitor.mutex.Unlock()

	now := time.Now()
	
	// 清理过期的请求记录
	cutoff := now.Add(-rl.window)
	validRequests := make([]time.Time, 0)
	for _, reqTime := range visitor.requests {
		if reqTime.After(cutoff) {
			validRequests = append(validRequests, reqTime)
		}
	}
	visitor.requests = validRequests

	// 检查是否超过限制
	if len(visitor.requests) >= rl.rate {
		return false
	}

	// 记录当前请求
	visitor.requests = append(visitor.requests, now)
	return true
}

// cleanup 清理过期的访问者记录
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(time.Minute * 5)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()
		cutoff := now.Add(-rl.window * 2) // 保留更长时间以避免频繁创建

		for ip, visitor := range rl.visitors {
			visitor.mutex.Lock()
			if len(visitor.requests) == 0 || 
				(len(visitor.requests) > 0 && visitor.requests[len(visitor.requests)-1].Before(cutoff)) {
				delete(rl.visitors, ip)
			}
			visitor.mutex.Unlock()
		}
		rl.mutex.Unlock()
	}
}

// DefaultRateLimit 默认速率限制中间件（每分钟100次请求）
func DefaultRateLimit() gin.HandlerFunc {
	limiter := NewRateLimiter(100, time.Minute)
	return limiter.RateLimit()
}
