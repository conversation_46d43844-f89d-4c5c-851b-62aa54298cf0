{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Tent = createLucideIcon(\"Tent\", [[\"path\", {\n  d: \"M19 20 10 4\",\n  key: \"1ak541\"\n}], [\"path\", {\n  d: \"m5 20 9-16\",\n  key: \"11dtj9\"\n}], [\"path\", {\n  d: \"M3 20h18\",\n  key: \"1l19wn\"\n}], [\"path\", {\n  d: \"m12 15-3 5\",\n  key: \"1c5kej\"\n}], [\"path\", {\n  d: \"m12 15 3 5\",\n  key: \"odkmhi\"\n}]]);\nexport { Tent as default };", "map": {"version": 3, "names": ["Tent", "createLucideIcon", "d", "key"], "sources": ["D:\\gohomeworkproject\\frontend\\node_modules\\lucide-react\\src\\icons\\tent.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tent\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjAgMTAgNCIgLz4KICA8cGF0aCBkPSJtNSAyMCA5LTE2IiAvPgogIDxwYXRoIGQ9Ik0zIDIwaDE4IiAvPgogIDxwYXRoIGQ9Im0xMiAxNS0zIDUiIC8+CiAgPHBhdGggZD0ibTEyIDE1IDMgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tent\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tent = createLucideIcon('Tent', [\n  ['path', { d: 'M19 20 10 4', key: '1ak541' }],\n  ['path', { d: 'm5 20 9-16', key: '11dtj9' }],\n  ['path', { d: 'M3 20h18', key: '1l19wn' }],\n  ['path', { d: 'm12 15-3 5', key: '1c5kej' }],\n  ['path', { d: 'm12 15 3 5', key: 'odkmhi' }],\n]);\n\nexport default Tent;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}