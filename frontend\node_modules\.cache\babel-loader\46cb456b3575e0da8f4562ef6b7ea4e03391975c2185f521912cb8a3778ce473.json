{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ToyBrick = createLucideIcon(\"ToyBrick\", [[\"rect\", {\n  width: \"18\",\n  height: \"12\",\n  x: \"3\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"158fvp\"\n}], [\"path\", {\n  d: \"M10 8V5c0-.6-.4-1-1-1H6a1 1 0 0 0-1 1v3\",\n  key: \"s0042v\"\n}], [\"path\", {\n  d: \"M19 8V5c0-.6-.4-1-1-1h-3a1 1 0 0 0-1 1v3\",\n  key: \"9wmeh2\"\n}]]);\nexport { ToyBrick as default };", "map": {"version": 3, "names": ["Toy<PERSON>rick", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\gohomeworkproject\\frontend\\node_modules\\lucide-react\\src\\icons\\toy-brick.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ToyBrick\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTIiIHg9IjMiIHk9IjgiIHJ4PSIxIiAvPgogIDxwYXRoIGQ9Ik0xMCA4VjVjMC0uNi0uNC0xLTEtMUg2YTEgMSAwIDAgMC0xIDF2MyIgLz4KICA8cGF0aCBkPSJNMTkgOFY1YzAtLjYtLjQtMS0xLTFoLTNhMSAxIDAgMCAwLTEgMXYzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/toy-brick\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToyBrick = createLucideIcon('ToyBrick', [\n  [\n    'rect',\n    { width: '18', height: '12', x: '3', y: '8', rx: '1', key: '158fvp' },\n  ],\n  ['path', { d: 'M10 8V5c0-.6-.4-1-1-1H6a1 1 0 0 0-1 1v3', key: 's0042v' }],\n  ['path', { d: 'M19 8V5c0-.6-.4-1-1-1h-3a1 1 0 0 0-1 1v3', key: '9wmeh2' }],\n]);\n\nexport default ToyBrick;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}