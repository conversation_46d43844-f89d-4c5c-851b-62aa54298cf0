package middleware

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"io"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerConfig 日志配置
type LoggerConfig struct {
	SkipPaths []string
}

// Logger 请求日志中间件
func Logger(config ...LoggerConfig) gin.HandlerFunc {
	var conf LoggerConfig
	if len(config) > 0 {
		conf = config[0]
	}

	return gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: func(param gin.LogFormatterParams) string {
			// 跳过指定路径
			for _, path := range conf.SkipPaths {
				if param.Path == path {
					return ""
				}
			}

			return formatLogEntry(param)
		},
		Output: gin.DefaultWriter,
	})
}

// RequestLogger 详细请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := generateRequestID()
		c.Set("request_id", requestID)

		// 记录请求开始时间
		start := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应写入器包装器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = writer

		// 记录请求信息
		logRequest(c, requestID, requestBody)

		// 处理请求
		c.Next()

		// 记录响应信息
		duration := time.Since(start)
		logResponse(c, requestID, writer.body.Bytes(), duration)
	}
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// formatLogEntry 格式化日志条目
func formatLogEntry(param gin.LogFormatterParams) string {
	logData := map[string]interface{}{
		"timestamp":  param.TimeStamp.Format("2006-01-02 15:04:05"),
		"status":     param.StatusCode,
		"latency":    param.Latency.String(),
		"client_ip":  param.ClientIP,
		"method":     param.Method,
		"path":       param.Path,
		"user_agent": param.Request.UserAgent(),
		"error":      param.ErrorMessage,
	}

	jsonData, _ := json.Marshal(logData)
	return string(jsonData) + "\n"
}

// logRequest 记录请求信息
func logRequest(c *gin.Context, requestID string, body []byte) {
	logData := map[string]interface{}{
		"type":       "request",
		"request_id": requestID,
		"timestamp":  time.Now().Format("2006-01-02 15:04:05"),
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"query":      c.Request.URL.RawQuery,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
		"headers":    getFilteredHeaders(c.Request.Header),
	}

	// 只记录非敏感的请求体
	if len(body) > 0 && len(body) < 1024 {
		var bodyData interface{}
		if err := json.Unmarshal(body, &bodyData); err == nil {
			logData["body"] = bodyData
		}
	}

	jsonData, _ := json.Marshal(logData)
	log.Printf("REQUEST: %s", string(jsonData))
}

// logResponse 记录响应信息
func logResponse(c *gin.Context, requestID string, body []byte, duration time.Duration) {
	logData := map[string]interface{}{
		"type":       "response",
		"request_id": requestID,
		"timestamp":  time.Now().Format("2006-01-02 15:04:05"),
		"status":     c.Writer.Status(),
		"duration":   duration.String(),
		"size":       len(body),
	}

	// 只记录非敏感的响应体
	if len(body) > 0 && len(body) < 1024 {
		var bodyData interface{}
		if err := json.Unmarshal(body, &bodyData); err == nil {
			logData["body"] = bodyData
		}
	}

	jsonData, _ := json.Marshal(logData)
	log.Printf("RESPONSE: %s", string(jsonData))
}

// getFilteredHeaders 获取过滤后的请求头
func getFilteredHeaders(headers map[string][]string) map[string]string {
	filtered := make(map[string]string)
	sensitiveHeaders := map[string]bool{
		"authorization": true,
		"cookie":        true,
		"x-api-key":     true,
	}

	for key, values := range headers {
		lowerKey := strings.ToLower(key)
		if !sensitiveHeaders[lowerKey] && len(values) > 0 {
			filtered[key] = values[0]
		}
	}

	return filtered
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return hex.EncodeToString(b)
}
