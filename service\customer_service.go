package service

import (
	"errors"
	"goHomework/model"
	"math"
	"regexp"
	"strings"
	"sync"
	"time"
)

type CustomerService struct {
	customers []model.Customer
	nextID    int
	mutex     sync.RWMutex
}

func NewCustomerService() *CustomerService {
	now := time.Now()
	return &CustomerService{
		customers: []model.Customer{
			{
				ID:          1,
				Name:        "张三",
				Gender:      "男",
				Age:         25,
				Phone:       "13800138001",
				Email:       "<EMAIL>",
				Status:      model.UserStatusActive,
				Avatar:      "",
				Address:     "北京市朝阳区",
				Description: "测试用户1",
				CreatedAt:   now.Add(-time.Hour * 24 * 30),
				UpdatedAt:   now.Add(-time.Hour * 24),
			},
			{
				ID:          2,
				Name:        "李四",
				Gender:      "女",
				Age:         30,
				Phone:       "13800138002",
				Email:       "<EMAIL>",
				Status:      model.UserStatusActive,
				Avatar:      "",
				Address:     "上海市浦东新区",
				Description: "测试用户2",
				CreatedAt:   now.Add(-time.Hour * 24 * 20),
				UpdatedAt:   now.Add(-time.Hour * 12),
			},
			{
				ID:          3,
				Name:        "王五",
				Gender:      "男",
				Age:         28,
				Phone:       "13800138003",
				Email:       "<EMAIL>",
				Status:      model.UserStatusActive,
				Avatar:      "",
				Address:     "广州市天河区",
				Description: "测试用户3",
				CreatedAt:   now.Add(-time.Hour * 24 * 10),
				UpdatedAt:   now.Add(-time.Hour * 6),
			},
		},
		nextID: 4,
	}
}

// GetAllCustomers 获取所有客户（已废弃，请使用 GetCustomerList）
func (s *CustomerService) GetAllCustomers() []model.Customer {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make([]model.Customer, 0)
	for _, customer := range s.customers {
		if customer.Status != model.UserStatusDeleted {
			result = append(result, customer)
		}
	}
	return result
}

// GetCustomerList 获取客户列表（支持分页和搜索）
func (s *CustomerService) GetCustomerList(req model.CustomerListRequest) (*model.CustomerListResponse, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 过滤数据
	var filteredCustomers []model.Customer
	for _, customer := range s.customers {
		// 状态过滤
		if req.Status != "" && customer.Status != req.Status {
			continue
		}

		// 性别过滤
		if req.Gender != "" && customer.Gender != req.Gender {
			continue
		}

		// 搜索过滤（姓名、邮箱、电话）
		if req.Search != "" {
			searchLower := strings.ToLower(req.Search)
			if !strings.Contains(strings.ToLower(customer.Name), searchLower) &&
				!strings.Contains(strings.ToLower(customer.Email), searchLower) &&
				!strings.Contains(customer.Phone, req.Search) {
				continue
			}
		}

		filteredCustomers = append(filteredCustomers, customer)
	}

	total := int64(len(filteredCustomers))
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 分页
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start > len(filteredCustomers) {
		start = len(filteredCustomers)
	}
	if end > len(filteredCustomers) {
		end = len(filteredCustomers)
	}

	pagedCustomers := filteredCustomers[start:end]

	return &model.CustomerListResponse{
		Data:       pagedCustomers,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

func (s *CustomerService) GetCustomerByID(id int) (*model.Customer, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, customer := range s.customers {
		if customer.ID == id && customer.Status != model.UserStatusDeleted {
			return &customer, nil
		}
	}
	return nil, errors.New("customer not found")
}

func (s *CustomerService) CreateCustomer(req model.CreateCustomerRequest) (*model.Customer, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 验证邮箱和手机号是否已存在
	if err := s.validateUniqueFields(req.Email, req.Phone, 0); err != nil {
		return nil, err
	}

	now := time.Now()
	customer := model.Customer{
		ID:          s.nextID,
		Name:        req.Name,
		Gender:      req.Gender,
		Age:         req.Age,
		Phone:       req.Phone,
		Email:       req.Email,
		Status:      model.UserStatusActive,
		Avatar:      req.Avatar,
		Address:     req.Address,
		Description: req.Description,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	s.nextID++
	s.customers = append(s.customers, customer)
	return &customer, nil
}

func (s *CustomerService) UpdateCustomer(id int, req model.UpdateCustomerRequest) (*model.Customer, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i, customer := range s.customers {
		if customer.ID == id && customer.Status != model.UserStatusDeleted {
			// 验证邮箱和手机号是否已存在（排除当前用户）
			if req.Email != nil {
				if err := s.validateUniqueFields(*req.Email, "", id); err != nil {
					return nil, err
				}
			}
			if req.Phone != nil {
				if err := s.validateUniqueFields("", *req.Phone, id); err != nil {
					return nil, err
				}
			}

			// 更新字段
			if req.Name != nil {
				customer.Name = *req.Name
			}
			if req.Gender != nil {
				customer.Gender = *req.Gender
			}
			if req.Age != nil {
				customer.Age = *req.Age
			}
			if req.Phone != nil {
				customer.Phone = *req.Phone
			}
			if req.Email != nil {
				customer.Email = *req.Email
			}
			if req.Avatar != nil {
				customer.Avatar = *req.Avatar
			}
			if req.Address != nil {
				customer.Address = *req.Address
			}
			if req.Description != nil {
				customer.Description = *req.Description
			}
			customer.UpdatedAt = time.Now()

			s.customers[i] = customer
			return &s.customers[i], nil
		}
	}
	return nil, errors.New("customer not found")
}

func (s *CustomerService) DeleteCustomer(id int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i, customer := range s.customers {
		if customer.ID == id && customer.Status != model.UserStatusDeleted {
			// 软删除：只更改状态
			s.customers[i].Status = model.UserStatusDeleted
			s.customers[i].UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("customer not found")
}

// UpdateCustomerStatus 更新用户状态
func (s *CustomerService) UpdateCustomerStatus(id int, status model.UserStatus) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i, customer := range s.customers {
		if customer.ID == id && customer.Status != model.UserStatusDeleted {
			s.customers[i].Status = status
			s.customers[i].UpdatedAt = time.Now()
			return nil
		}
	}
	return errors.New("customer not found")
}

// BatchDeleteCustomers 批量删除用户
func (s *CustomerService) BatchDeleteCustomers(ids []int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	for i := range s.customers {
		for _, id := range ids {
			if s.customers[i].ID == id && s.customers[i].Status != model.UserStatusDeleted {
				s.customers[i].Status = model.UserStatusDeleted
				s.customers[i].UpdatedAt = now
				break
			}
		}
	}
	return nil
}

// GetCustomerStats 获取用户统计信息
func (s *CustomerService) GetCustomerStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"total":    0,
		"active":   0,
		"inactive": 0,
		"blocked":  0,
		"deleted":  0,
		"by_gender": map[string]int{
			"男":  0,
			"女":  0,
			"其他": 0,
		},
	}

	for _, customer := range s.customers {
		stats["total"] = stats["total"].(int) + 1

		switch customer.Status {
		case model.UserStatusActive:
			stats["active"] = stats["active"].(int) + 1
		case model.UserStatusInactive:
			stats["inactive"] = stats["inactive"].(int) + 1
		case model.UserStatusBlocked:
			stats["blocked"] = stats["blocked"].(int) + 1
		case model.UserStatusDeleted:
			stats["deleted"] = stats["deleted"].(int) + 1
		}

		if genderStats, ok := stats["by_gender"].(map[string]int); ok {
			if customer.Gender != "" {
				genderStats[customer.Gender]++
			}
		}
	}

	return stats
}

// validateUniqueFields 验证邮箱和手机号的唯一性
func (s *CustomerService) validateUniqueFields(email, phone string, excludeID int) error {
	for _, customer := range s.customers {
		if customer.ID == excludeID || customer.Status == model.UserStatusDeleted {
			continue
		}

		if email != "" && customer.Email == email {
			return errors.New("邮箱已存在")
		}

		if phone != "" && customer.Phone == phone {
			return errors.New("手机号已存在")
		}
	}
	return nil
}

// validateEmail 验证邮箱格式
func (s *CustomerService) validateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return errors.New("邮箱格式不正确")
	}
	return nil
}

// validatePhone 验证手机号格式
func (s *CustomerService) validatePhone(phone string) error {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		return errors.New("手机号格式不正确")
	}
	return nil
}

// SearchCustomers 搜索用户（模糊搜索）
func (s *CustomerService) SearchCustomers(keyword string) []model.Customer {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var result []model.Customer
	keywordLower := strings.ToLower(keyword)

	for _, customer := range s.customers {
		if customer.Status == model.UserStatusDeleted {
			continue
		}

		if strings.Contains(strings.ToLower(customer.Name), keywordLower) ||
			strings.Contains(strings.ToLower(customer.Email), keywordLower) ||
			strings.Contains(customer.Phone, keyword) ||
			strings.Contains(strings.ToLower(customer.Address), keywordLower) {
			result = append(result, customer)
		}
	}

	return result
}
