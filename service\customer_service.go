package service

import (
	"errors"
	"goHomework/model"
	"sync"
)

type CustomerService struct {
	customers []model.Customer
	nextID    int
	mutex     sync.RWMutex
}

func NewCustomerService() *CustomerService {
	return &CustomerService{
		customers: []model.Customer{
			{Id: 1, Name: "张三", Gender: "男", Age: 25, Phone: "13800138001", Email: "<EMAIL>"},
			{Id: 2, Name: "李四", Gender: "女", Age: 30, Phone: "13800138002", Email: "<EMAIL>"},
			{Id: 3, Name: "王五", Gender: "男", Age: 28, Phone: "13800138003", Email: "<EMAIL>"},
		},
		nextID: 4,
	}
}

func (s *CustomerService) GetAllCustomers() []model.Customer {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	result := make([]model.Customer, len(s.customers))
	copy(result, s.customers)
	return result
}

func (s *CustomerService) GetCustomerByID(id int) (*model.Customer, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	for _, customer := range s.customers {
		if customer.Id == id {
			return &customer, nil
		}
	}
	return nil, errors.New("customer not found")
}

func (s *CustomerService) CreateCustomer(customer model.Customer) model.Customer {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	customer.Id = s.nextID
	s.nextID++
	s.customers = append(s.customers, customer)
	return customer
}

func (s *CustomerService) UpdateCustomer(id int, updatedCustomer model.Customer) (*model.Customer, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	for i, customer := range s.customers {
		if customer.Id == id {
			updatedCustomer.Id = id
			s.customers[i] = updatedCustomer
			return &s.customers[i], nil
		}
	}
	return nil, errors.New("customer not found")
}

func (s *CustomerService) DeleteCustomer(id int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	for i, customer := range s.customers {
		if customer.Id == id {
			s.customers = append(s.customers[:i], s.customers[i+1:]...)
			return nil
		}
	}
	return errors.New("customer not found")
}
