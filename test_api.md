# API 测试指南

## 测试环境

确保服务器正在运行：
```bash
go run main.go
```

服务器将在 `http://localhost:8080` 启动。

## 测试用例

### 1. 健康检查

```bash
# 使用 curl
curl -X GET http://localhost:8080/health

# 使用 PowerShell
Invoke-WebRequest -Uri http://localhost:8080/health -Method GET
```

预期响应：
```json
{
  "status": "ok",
  "message": "服务器运行正常",
  "timestamp": "2025-09-03 17:49:19",
  "version": "1.0.0"
}
```

### 2. 获取用户列表

```bash
# 获取第一页，每页10条
curl -X GET "http://localhost:8080/api/v1/customers?page=1&page_size=10"

# 搜索用户
curl -X GET "http://localhost:8080/api/v1/customers?search=张三"

# 按状态过滤
curl -X GET "http://localhost:8080/api/v1/customers?status=active"
```

### 3. 获取用户统计信息

```bash
curl -X GET http://localhost:8080/api/v1/customers/stats
```

### 4. 获取单个用户

```bash
curl -X GET http://localhost:8080/api/v1/customers/1
```

### 5. 创建用户

```bash
curl -X POST http://localhost:8080/api/v1/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试用户",
    "gender": "男",
    "age": 25,
    "phone": "13800138888",
    "email": "<EMAIL>",
    "address": "测试地址",
    "description": "这是一个测试用户"
  }'
```

### 6. 更新用户

```bash
curl -X PUT http://localhost:8080/api/v1/customers/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的姓名",
    "age": 26
  }'
```

### 7. 更新用户状态

```bash
curl -X PATCH http://localhost:8080/api/v1/customers/1/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "inactive"
  }'
```

### 8. 搜索用户

```bash
curl -X GET "http://localhost:8080/api/v1/customers/search?keyword=张三"
```

### 9. 批量删除用户

```bash
curl -X DELETE http://localhost:8080/api/v1/customers/batch \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2]
  }'
```

### 10. 删除用户

```bash
curl -X DELETE http://localhost:8080/api/v1/customers/1
```

## PowerShell 测试示例

如果使用 PowerShell，可以使用以下命令：

```powershell
# 获取用户列表
$response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/customers" -Method GET
$response.Content | ConvertFrom-Json

# 创建用户
$body = @{
    name = "PowerShell测试用户"
    gender = "女"
    age = 28
    phone = "13800139999"
    email = "<EMAIL>"
    address = "PowerShell测试地址"
    description = "通过PowerShell创建的用户"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/customers" -Method POST -Body $body -ContentType "application/json"
$response.Content | ConvertFrom-Json
```

## 验证功能

1. **分页功能**：测试不同的 page 和 page_size 参数
2. **搜索功能**：测试按姓名、邮箱、电话搜索
3. **状态过滤**：测试按用户状态过滤
4. **数据验证**：尝试提交无效数据，验证错误处理
5. **速率限制**：快速发送多个请求，验证速率限制
6. **CORS**：从不同域名发送请求，验证跨域支持

## 预期的响应格式

所有成功的响应都应该包含：
- `success`: true
- `message`: 操作描述
- `data`: 实际数据
- `timestamp`: 时间戳
- `request_id`: 请求ID（如果有）

错误响应应该包含：
- `success`: false
- `message`: 错误描述
- `error`: 错误详情
- `timestamp`: 时间戳
- `request_id`: 请求ID（如果有）
