package middleware

import (
	"fmt"
	"goHomework/model"
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// Recovery 错误恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 记录错误信息
		logPanic(c, recovered)

		// 返回统一错误响应
		resp := model.NewResponseBuilder(c)
		resp.Error(http.StatusInternalServerError, model.ErrCodeInternalError, "服务器内部错误", nil)
		
		c.Abort()
	})
}

// logPanic 记录panic信息
func logPanic(c *gin.Context, recovered interface{}) {
	// 获取堆栈信息
	stack := debug.Stack()
	
	// 构建错误日志
	errorLog := fmt.Sprintf(
		"[PANIC RECOVERED] %s %s\n"+
			"Error: %v\n"+
			"Stack: %s\n"+
			"Request ID: %s\n"+
			"Client IP: %s\n"+
			"User Agent: %s",
		c.Request.Method,
		c.Request.URL.Path,
		recovered,
		string(stack),
		c.GetString("request_id"),
		c.ClientIP(),
		c.Request.UserAgent(),
	)
	
	log.Printf("PANIC: %s", errorLog)
}
